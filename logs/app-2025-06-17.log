2025-06-17T00:04:29.567Z [INFO] [MihomoConfigGenerator] 成功生成Mihomo配置: 4 个代理节点
2025-06-17T00:04:29.568Z [WARN] [MihomoConfigGenerator] 不支持的代理类型: unsupported
2025-06-17T00:04:29.592Z [DEBUG] [VmessParser] 成功解析VMess节点: TestNode
2025-06-17T00:04:29.593Z [DEBUG] [VlessParser] 成功解析VLESS节点: TestNode
2025-06-17T00:04:29.593Z [DEBUG] [TrojanParser] 成功解析Trojan节点: TestNode
2025-06-17T00:04:29.593Z [DEBUG] [ShadowsocksParser] 成功解析Shadowsocks节点: TestNode
2025-06-17T00:04:29.598Z [DEBUG] [VmessParser] 成功解析VMess节点: Node1
2025-06-17T00:04:29.598Z [DEBUG] [VlessParser] 成功解析VLESS节点: Node2
2025-06-17T00:04:29.598Z [INFO] [SubscriptionParser] 成功解析 2 个节点
2025-06-17T00:04:29.599Z [WARN] [VmessParser] 无效的VMess URL格式: invalid://url
2025-06-17T00:04:29.599Z [WARN] [VlessParser] 无效的VLESS URL格式: invalid://url
2025-06-17T00:04:29.599Z [WARN] [TrojanParser] 无效的Trojan URL格式: invalid://url
2025-06-17T00:04:29.599Z [WARN] [ShadowsocksParser] 无效的Shadowsocks URL格式: invalid://url
2025-06-17T00:04:29.599Z [ERROR] [VmessParser] 解析VMess URL失败: Base64解码失败: Failed to decode base64 {"url":"vmess://invalid-base64"}
2025-06-17T00:04:29.600Z [WARN] [VlessParser] VLESS URL缺少必需字段
2025-06-17T00:22:06.230Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-17T00:22:06.231Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-17T00:22:06.231Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-17T00:30:27.955Z [INFO] [Main] 收到 SIGINT 信号，正在关闭应用...
2025-06-17T00:30:27.958Z [INFO] [Main] Sub Node Manage 停止中...
2025-06-17T00:30:27.960Z [INFO] [DatabaseManager] 数据库连接已关闭
2025-06-17T00:30:27.960Z [INFO] [ApiServer] API服务器已停止
2025-06-17T00:30:27.960Z [INFO] [Main] Sub Node Manage 已停止
2025-06-17T00:34:05.400Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-17T00:34:05.402Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-17T00:34:05.402Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-17T00:38:01.174Z [INFO] [ApiServer] GET /api/health - 200 - 0ms
2025-06-17T00:38:06.401Z [INFO] [ApiServer] HEAD / - 200 - 1ms
2025-06-17T00:38:11.909Z [INFO] [ApiServer] GET / - 200 - 0ms
2025-06-17T00:38:17.540Z [INFO] [ApiServer] HEAD /assets/index-D_sw2uUy.js - 200 - 3ms
2025-06-17T00:41:23.378Z [INFO] [ApiServer] GET /api/health - 200 - 0ms
2025-06-17T00:41:28.407Z [INFO] [ApiServer] GET /api/nodes - 200 - 4ms
2025-06-17T00:41:34.807Z [INFO] [ApiServer] HEAD /assets/index-COm7Ufi-.js - 200 - 0ms
2025-06-17T00:41:47.290Z [INFO] [MihomoConfigGenerator] 成功生成Mihomo配置: 4 个代理节点
2025-06-17T00:41:47.290Z [WARN] [MihomoConfigGenerator] 不支持的代理类型: unsupported
2025-06-17T00:41:47.313Z [DEBUG] [VmessParser] 成功解析VMess节点: TestNode
2025-06-17T00:41:47.314Z [DEBUG] [VlessParser] 成功解析VLESS节点: TestNode
2025-06-17T00:41:47.314Z [DEBUG] [TrojanParser] 成功解析Trojan节点: TestNode
2025-06-17T00:41:47.314Z [DEBUG] [ShadowsocksParser] 成功解析Shadowsocks节点: TestNode
2025-06-17T00:41:47.321Z [DEBUG] [VmessParser] 成功解析VMess节点: Node1
2025-06-17T00:41:47.321Z [DEBUG] [VlessParser] 成功解析VLESS节点: Node2
2025-06-17T00:41:47.321Z [INFO] [SubscriptionParser] 成功解析 2 个节点
2025-06-17T00:41:47.321Z [WARN] [VmessParser] 无效的VMess URL格式: invalid://url
2025-06-17T00:41:47.321Z [WARN] [VlessParser] 无效的VLESS URL格式: invalid://url
2025-06-17T00:41:47.321Z [WARN] [TrojanParser] 无效的Trojan URL格式: invalid://url
2025-06-17T00:41:47.321Z [WARN] [ShadowsocksParser] 无效的Shadowsocks URL格式: invalid://url
2025-06-17T00:41:47.321Z [ERROR] [VmessParser] 解析VMess URL失败: Base64解码失败: Failed to decode base64 {"url":"vmess://invalid-base64"}
2025-06-17T00:41:47.322Z [WARN] [VlessParser] VLESS URL缺少必需字段
2025-06-17T00:41:53.482Z [INFO] [ApiServer] GET / - 200 - 1ms
2025-06-17T00:41:53.509Z [INFO] [ApiServer] GET /assets/index-COm7Ufi-.js - 200 - 2ms
2025-06-17T00:41:53.525Z [INFO] [ApiServer] GET /assets/index-DbKwsSps.css - 200 - 17ms
2025-06-17T00:41:53.538Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 1ms
2025-06-17T00:41:53.540Z [INFO] [ApiServer] GET /api/nodes - 200 - 1ms
2025-06-17T00:41:53.700Z [INFO] [ApiServer] GET /vite.svg - 200 - 0ms
2025-06-17T00:45:04.964Z [INFO] [ApiServer] GET / - 200 - 1ms
2025-06-17T00:45:04.982Z [INFO] [ApiServer] GET /assets/index-COm7Ufi-.js - 200 - 1ms
2025-06-17T00:45:04.992Z [INFO] [ApiServer] GET /assets/index-DbKwsSps.css - 200 - 10ms
2025-06-17T00:45:05.001Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 1ms
2025-06-17T00:45:05.001Z [INFO] [ApiServer] GET /api/nodes - 200 - 0ms
2025-06-17T00:45:05.021Z [INFO] [ApiServer] GET /vite.svg - 200 - 1ms
2025-06-17T00:45:13.361Z [INFO] [ApiServer] GET / - 200 - 0ms
2025-06-17T00:45:13.379Z [INFO] [ApiServer] GET /assets/index-COm7Ufi-.js - 200 - 0ms
2025-06-17T00:45:13.390Z [INFO] [ApiServer] GET /assets/index-DbKwsSps.css - 200 - 1ms
2025-06-17T00:45:13.402Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 0ms
2025-06-17T00:45:13.403Z [INFO] [ApiServer] GET /api/nodes - 200 - 1ms
2025-06-17T00:45:29.028Z [INFO] [ApiServer] GET / - 200 - 1ms
2025-06-17T00:45:29.047Z [INFO] [ApiServer] GET /assets/index-DbKwsSps.css - 200 - 0ms
2025-06-17T00:45:29.048Z [INFO] [ApiServer] GET /assets/index-COm7Ufi-.js - 200 - 1ms
2025-06-17T00:45:29.066Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 0ms
2025-06-17T00:45:29.067Z [INFO] [ApiServer] GET /api/nodes - 200 - 0ms
2025-06-17T00:45:29.083Z [INFO] [ApiServer] GET /vite.svg - 200 - 1ms
2025-06-17T00:45:43.773Z [INFO] [SubscriptionParser] 开始解析订阅: https://vpn.huasuan666.top/api/v1/client/subscribe?token=3a53934da27b4d846783b9dd67f44d60
2025-06-17T00:45:44.703Z [WARN] [SubscriptionParser] 未知的协议类型: mixed-port: 7890
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: allow-lan: true
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: bind-address: '*'
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: mode: rule
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: log-level: info
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: external-controller: '127.0.0.1:9090'
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: dns:
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: enable: true
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: ipv6: false
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: default-nameserver: [*********, ************, ***************]
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: enhanced-mode: fake-ip
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: fake-ip-range: **********/16
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: use-hosts: true
2025-06-17T00:45:44.704Z [WARN] [SubscriptionParser] 未知的协议类型: respect-rules: true
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: proxy-server-nameserver: [*********, ************, ***************]
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: nameserver: [*********, ************, ***************]
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: fallback: [*******, *******]
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: fallback-filter: { geoip: true, geoip-code: CN, geosite: [gfw], ipcidr: [240.0.0.0/4], domain: [+.google.com, +.facebook.com, +.youtube.com] }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: proxies:
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '剩余流量：399.7 GB', server: hsmg.huasuan666.top, port: 22151, udp: true, skip-cert-verify: true, sni: hsmg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 套餐到期：长期有效, server: hsmg.huasuan666.top, port: 22151, udp: true, skip-cert-verify: true, sni: hsmg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 美国, server: hsmg.huasuan666.top, port: 22151, udp: true, skip-cert-verify: true, sni: hsmg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 香港, server: hsxg.huasuan666.top, port: 22153, udp: true, skip-cert-verify: true, sni: hsxg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 新加坡, server: hsxjp.huasuan666.top, port: 22154, udp: true, skip-cert-verify: true, sni: hsxjp.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 德国, server: hsdg.huasuan666.top, port: 22155, udp: true, skip-cert-verify: true, sni: hsdg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 英国, server: hsyg.huasuan666.top, port: 22156, udp: true, skip-cert-verify: true, sni: hsyg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 荷兰, server: hshl.huasuan666.top, port: 22157, udp: true, skip-cert-verify: true, sni: hshl.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 法国, server: hsfg.huasuan666.top, port: 22158, udp: true, skip-cert-verify: true, sni: hsfg.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 澳大利亚, server: hsadly.huasuan666.top, port: 22159, udp: true, skip-cert-verify: true, sni: hsadly.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 波兰, server: hsbl.huasuan666.top, port: 22160, udp: true, skip-cert-verify: true, sni: hsbl.huasuan666.top, type: hysteria2, password: 927f1be9-7e0e-4905-b0d1-1639e20a4049 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: proxy-groups:
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 划算机场, type: select, proxies: [自动选择, 故障转移, '剩余流量：399.7 GB', 套餐到期：长期有效, 美国, 香港, 新加坡, 德国, 英国, 荷兰, 法国, 澳大利亚, 波兰] }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 自动选择, type: url-test, proxies: ['剩余流量：399.7 GB', 套餐到期：长期有效, 美国, 香港, 新加坡, 德国, 英国, 荷兰, 法国, 澳大利亚, 波兰], url: 'http://www.gstatic.com/generate_204', interval: 86400 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 故障转移, type: fallback, proxies: ['剩余流量：399.7 GB', 套餐到期：长期有效, 美国, 香港, 新加坡, 德国, 英国, 荷兰, 法国, 澳大利亚, 波兰], url: 'http://www.gstatic.com/generate_204', interval: 7200 }
2025-06-17T00:45:44.705Z [WARN] [SubscriptionParser] 未知的协议类型: rules:
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,*******/32,划算机场,no-resolve'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,*******/32,划算机场,no-resolve'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,services.googleapis.cn,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xn--ngstr-lra8j.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,safebrowsing.urlsec.qq.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,safebrowsing.googleapis.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,developer.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,digicert.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,ocsp.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,ocsp.comodoca.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,ocsp.usertrust.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,ocsp.sectigo.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,ocsp.verisign.net,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apple-dns.net,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,testflight.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,sandbox.itunes.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,itunes.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apps.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blobstore.apple.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,cvws.icloud-content.com,划算机场'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mzstatic.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,itunes.apple.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,icloud.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,icloud-content.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,me.com,DIRECT'
2025-06-17T00:45:44.706Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,aaplimg.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cdn20.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cdn-apple.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,akadns.net,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,akamaiedge.net,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,edgekey.net,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mwcloudcdn.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mwcname.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apple.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apple-cloudkit.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apple-mapkit.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,cn.bing.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,126.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,126.net,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,127.net,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,163.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,360buyimg.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,36kr.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,acfun.tv,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,air-matters.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,aixifan.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,alicdn,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,alipay,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,taobao,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,amap.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,autonavi.com,DIRECT'
2025-06-17T00:45:44.707Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,baidu,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bdimg.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bdstatic.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bilibili.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bilivideo.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,caiyunapp.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,clouddn.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cnbeta.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cnbetacdn.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cootekservice.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,csdn.net,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ctrip.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dgtle.com,DIRECT'
2025-06-17T00:45:44.708Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dianping.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,douban.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,doubanio.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,duokan.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,easou.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ele.me,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,feng.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fir.im,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,frdic.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,g-cores.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,godic.net,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gtimg.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,cdn.hockeyapp.net,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,hongxiu.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,hxcdn.net,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,iciba.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ifeng.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ifengimg.com,DIRECT'
2025-06-17T00:45:44.710Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ipip.net,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,iqiyi.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,jd.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,jianshu.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,knewone.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,le.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,lecloud.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,lemicp.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,licdn.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,luoo.net,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,meituan.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,meituan.net,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mi.com,DIRECT'
2025-06-17T00:45:44.711Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,miaopai.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,microsoft.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,microsoftonline.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,miui.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,miwifi.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mob.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,netease.com,DIRECT'
2025-06-17T00:45:44.712Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,office.com,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,office365.com,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,officecdn,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,oschina.net,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ppsimg.com,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pstatp.com,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qcloud.com,DIRECT'
2025-06-17T00:45:44.713Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qdaily.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qdmm.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qhimg.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qhres.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qidian.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qihucdn.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qiniu.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qiniucdn.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qiyipic.com,DIRECT'
2025-06-17T00:45:44.715Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qq.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,qqurl.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,rarbg.to,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ruguoapp.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,segmentfault.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sinaapp.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,smzdm.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,snapdrop.net,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sogou.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sogoucdn.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sohu.com,DIRECT'
2025-06-17T00:45:44.716Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,soku.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,speedtest.net,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sspai.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,suning.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,taobao.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tencent.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tenpay.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tianyancha.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tudou.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tmall.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,umetrip.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,upaiyun.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,upyun.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,veryzhun.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,weather.com,DIRECT'
2025-06-17T00:45:44.717Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,weibo.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xiami.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xiami.net,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xiaomicp.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ximalaya.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xmcdn.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xunlei.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yhd.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yihaodianimg.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yinxiang.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ykimg.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,youdao.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,youku.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,zealer.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,zhihu.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,zhimg.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,zimuzu.tv,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,zoho.com,DIRECT'
2025-06-17T00:45:44.718Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,amazon,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,google,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,gmail,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,youtube,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,facebook,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fb.me,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fbcdn.net,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,twitter,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,instagram,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,dropbox,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,twimg.com,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,blogspot,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,youtu.be,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,whatsapp,划算机场'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,admarvel,REJECT'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,admaster,REJECT'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,adsage,REJECT'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,adsmogo,REJECT'
2025-06-17T00:45:44.719Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,adsrvmedia,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,adwords,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,adservice,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,appsflyer.com,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,domob,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,doubleclick.net,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,duomeng,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,dwtrack,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,guanggao,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,lianmeng,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mmstat.com,REJECT'
2025-06-17T00:45:44.721Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,mopub,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,omgmta,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,openx,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,partnerad,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,pingfore,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,supersonicads,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,uedas,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,umeng,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,usage,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vungle.com,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,wlmonitor,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,zjtoolbar,REJECT'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,9to5mac.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,abpchina.org,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,adblockplus.org,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,adobe.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,akamaized.net,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,alfredapp.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,amplitude.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ampproject.org,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,android.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,angularjs.org,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,aolcdn.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,apkpure.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,appledaily.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,appshopper.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,appspot.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,arcgis.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,archive.org,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,armorgames.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,aspnetcdn.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,att.com,划算机场'
2025-06-17T00:45:44.722Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,awsstatic.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,azureedge.net,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,azurewebsites.net,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bing.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bintray.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bit.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bit.ly,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bitbucket.org,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bjango.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bkrtx.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blog.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blogcdn.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blogger.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blogsmithmedia.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blogspot.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,blogspot.hk,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,bloomberg.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,box.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,box.net,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cachefly.net,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,chromium.org,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cl.ly,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cloudflare.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cloudfront.net,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cloudmagic.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cmail19.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cnet.com,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cocoapods.org,划算机场'
2025-06-17T00:45:44.723Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,comodoca.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,crashlytics.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,culturedcode.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,d.pr,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,danilo.to,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dayone.me,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,db.tt,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,deskconnect.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,disq.us,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,disqus.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,disquscdn.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dnsimple.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,docker.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dribbble.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,droplr.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,duckduckgo.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dueapp.com,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,dytt8.net,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,edgecastcdn.net,划算机场'
2025-06-17T00:45:44.724Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,edgekey.net,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,edgesuite.net,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,engadget.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,entrust.net,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,eurekavpt.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,evernote.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fabric.io,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fast.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fastly.net,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fc2.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,feedburner.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,feedly.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,feedsportal.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,fiftythree.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,firebaseio.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,flexibits.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,flickr.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,flipboard.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,g.co,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gabia.net,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,geni.us,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gfx.ms,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ggpht.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ghostnoteapp.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,git.io,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,github,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,globalsign.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gmodules.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,godaddy.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,golang.org,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gongm.in,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,goo.gl,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,goodreaders.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,goodreads.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gravatar.com,划算机场'
2025-06-17T00:45:44.725Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gstatic.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,gvt0.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,hockeyapp.net,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,hotmail.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,icons8.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ifixit.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ift.tt,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ifttt.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,iherb.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,imageshack.us,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,img.ly,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,imgur.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,imore.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,instapaper.com,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ipn.li,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,is.gd,划算机场'
2025-06-17T00:45:44.726Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,issuu.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,itgonglun.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,itun.es,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ixquick.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,j.mp,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,js.revsci.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,jshint.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,jtvnw.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,justgetflux.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,kat.cr,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,klip.me,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,libsyn.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,linkedin.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,line-apps.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,linode.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,lithium.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,littlehj.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,live.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,live.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,livefilestore.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,llnwd.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,macid.co,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,macromedia.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,macrumors.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mashable.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mathjax.org,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,medium.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mega.co.nz,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mega.nz,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,megaupload.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,microsofttranslator.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mindnode.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,mobile01.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,modmyi.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,msedge.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,myfontastic.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,name.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,nextmedia.com,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,nsstatic.net,划算机场'
2025-06-17T00:45:44.727Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,nssurge.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,nyt.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,nytimes.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,omnigroup.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,onedrive.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,onenote.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ooyala.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,openvpn.net,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,openwrt.org,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,orkut.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,osxdaily.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,outlook.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ow.ly,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,paddleapi.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,parallels.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,parse.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pdfexpert.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,periscope.tv,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pinboard.in,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pinterest.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pixelmator.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pixiv.net,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,playpcesor.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,playstation.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,playstation.com.hk,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,playstation.net,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,playstationnetwork.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,pushwoosh.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,rime.im,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,servebom.com,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sfx.ms,划算机场'
2025-06-17T00:45:44.728Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,shadowsocks.org,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sharethis.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,shazam.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,skype.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,smartdns划算机场.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,smartmailcloud.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sndcdn.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sony.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,soundcloud.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sourceforge.net,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,spotify.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,squarespace.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,sstatic.net,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,st.luluku.pw,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,stackoverflow.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,startpage.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,staticflickr.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,steamcommunity.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,symauth.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,symcb.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,symcd.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tapbots.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tapbots.net,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tdesktop.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,techcrunch.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,techsmith.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,thepiratebay.org,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,theverge.com,划算机场'
2025-06-17T00:45:44.729Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,time.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,timeinc.net,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tiny.cc,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tinypic.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tmblr.co,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,todoist.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,trello.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,trustasiassl.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tumblr.co,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tumblr.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tweetdeck.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,tweetmarker.net,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,twitch.tv,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,txmblr.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,typekit.net,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ubertags.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ublock.org,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ubnt.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ulyssesapp.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,urchin.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,usertrust.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,v.gd,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,v2ex.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vimeo.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vimeocdn.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vine.co,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vivaldi.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vox-cdn.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vsco.co,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,vultr.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,w.org,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,w3schools.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,webtype.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wikiwand.com,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wikimedia.org,划算机场'
2025-06-17T00:45:44.730Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wikileaks.org,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wikipedia.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wikipedia.org,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,windows.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,windows.net,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wire.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wordpress.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,workflowy.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wp.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wsj.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,wsj.net,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xda-developers.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xeeno.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,xiti.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yahoo.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yimg.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ying.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,yoyo.org,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,ytimg.com,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,telegra.ph,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,telegram.org,划算机场'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**********/22,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**********/21,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***********/22,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***********/22,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,*************/20,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR6,2001:67c:4e8::/48,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR6,2001:b28:f23d::/48,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR6,2001:b28:f23f::/48,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**************/32,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,************/24,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,************/24,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,************/24,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,************/24,划算机场,no-resolve'
2025-06-17T00:45:44.731Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,************/24,划算机场,no-resolve'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***************/32,划算机场,no-resolve'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**************/32,划算机场,no-resolve'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,injections.adguard.org,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,local.adguard.org,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,local,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,*********/8,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**********/12,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,***********/16,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,10.0.0.0/8,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,********/8,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,**********/10,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR,*********/4,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'IP-CIDR6,fe80::/10,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-SUFFIX,cn,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN-KEYWORD,-cn,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'GEOIP,CN,DIRECT'
2025-06-17T00:45:44.732Z [WARN] [SubscriptionParser] 未知的协议类型: - 'MATCH,划算机场'
2025-06-17T00:45:44.732Z [INFO] [SubscriptionParser] 成功解析 0 个节点
2025-06-17T00:45:44.732Z [INFO] [SubscriptionParser] 去重前: 0 个节点，去重后: 0 个节点
2025-06-17T00:45:44.732Z [INFO] [SubscriptionParser] 批量解析完成: 0 个节点，0 个错误
2025-06-17T00:45:44.732Z [INFO] [ApiServer] POST /api/subscriptions/parse - 200 - 963ms
2025-06-17T00:45:44.736Z [INFO] [ApiServer] GET /api/nodes - 200 - 0ms
2025-06-17T00:46:29.860Z [INFO] [SubscriptionParser] 开始解析订阅: https://submit.xz61.cn:23443/api/v1/client/subscribe?token=38b89c466e2e22ea510257861b485f16
2025-06-17T00:46:30.756Z [WARN] [SubscriptionParser] 未知的协议类型: mixed-port: 7890
2025-06-17T00:46:30.756Z [WARN] [SubscriptionParser] 未知的协议类型: allow-lan: true
2025-06-17T00:46:30.756Z [WARN] [SubscriptionParser] 未知的协议类型: bind-address: '*'
2025-06-17T00:46:30.756Z [WARN] [SubscriptionParser] 未知的协议类型: mode: rule
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: log-level: info
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: external-controller: '127.0.0.1:9090'
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: dns:
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: enable: true
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: ipv6: false
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: default-nameserver: [*********, *********]
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: proxy-server-nameserver: ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query', 'https://*********/dns-query', 'https://*********/dns-query', 'tls://dns.alidns.com', 'tls://*********:853', 'tls://*********:853']
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: enhanced-mode: fake-ip
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: fake-ip-range: **********/16
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: fake-ip-filter: ['*.lan', localhost.ptlogin2.qq.com]
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: use-hosts: true
2025-06-17T00:46:30.757Z [WARN] [SubscriptionParser] 未知的协议类型: nameserver: ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query', 'https://*********/dns-query', 'https://*********/dns-query', 'tls://dns.alidns.com', 'tls://*********:853', 'tls://*********:853']
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: fallback: ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query', 'https://*********/dns-query', 'https://*********/dns-query', 'tls://dns.alidns.com', 'tls://*********:853', 'tls://*********:853']
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: fallback-filter: { geoip: true, ipcidr: [240.0.0.0/4, 0.0.0.0/32] }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: proxies:
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 剩余流量：0, server: c91b54600a15afd177c702dfbea4b3be.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '距离下次重置剩余：2 天', server: 496e5c65a54c1eb5b59a2827dd9fd58f.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 套餐到期：2025-06-19, server: 387167bee6251ff981761e1d0542c94b.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V0-美国-软件需开启跳过证书验证！！！, server: f37fef66ae8604b8afda99b5433683e7.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V0-美国-官网：www.cac.mom, server: 4db79f30f195a1adb4a2a8bdbf2ad706.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V0-美国-出现问题请先更新订阅！！！, server: d1fd28f55db1095df19eb6ac65059bcd.us.in.node-is.green, port: 61666, sni: www.iq.com, up: 50000, down: 50000, skip-cert-verify: true, type: hysteria2, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, obfs: salamander, obfs-password: salamander }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-香港3-HKT, type: trojan, server: fd426584819ba6e4910f083a2ec90070.v1.cac.node-is.green, port: 44955, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: hk13.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-香港4-HKT, type: trojan, server: 658eead5f19ac3505ae12a1c66346db2.v1.cac.node-is.green, port: 40801, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: hk14.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-台湾-IPv6, type: trojan, server: eda614a4faf6f2eb0fb0136f05c89695.v1.cac.node-is.green, port: 40848, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: tw1.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-新加坡-IPv6, type: trojan, server: 915ccf2803061c7a59f7f6a305226ce5.v1.cac.node-is.green, port: 49897, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: sg1.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-加拿大-IPv6, type: trojan, server: 27b247a6d851ae350e71476a24743188.v1.cac.node-is.green, port: 40725, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ca1.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-日本1-IPv6, type: trojan, server: 3c7feb97752b1c9f9cfa4790bd9c9ebf.v1.cac.node-is.green, port: 45617, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: jp1.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-美国-IPv6, type: trojan, server: b64868a17f5badbf0f75ed15e55e00a7.v1.cac.node-is.green, port: 49500, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: us1.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-韩国, type: trojan, server: 37376fed2257c2718deb592e9d38a92f.v1.cac.node-is.green, port: 40360, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: kr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-英国, type: trojan, server: 53d4348aa5ba3978f5299b5b9ff24170.v1.cac.node-is.green, port: 49115, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gb.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.758Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-马来西亚, type: trojan, server: 886c2776b108febcd5492e0878d0131e.v1.cac.node-is.green, port: 47305, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: my.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-菲律宾, type: trojan, server: efcf3965b6a1431f76437329d6c9acc0.v1.cac.node-is.green, port: 42672, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ph.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-加拿大, type: trojan, server: af6fa950b7754ab5e7dc369aee74e449.v1.cac.node-is.green, port: 42547, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ca.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-阿根廷, type: trojan, server: 3f7a66b16074b2192da91e9098558b27.v1.cac.node-is.green, port: 44795, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ar.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-印度, type: trojan, server: b3b76e9ed698017b538feba3e52e0ae2.v1.cac.node-is.green, port: 48728, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: in.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-墨西哥, type: trojan, server: 741cb9919fe83ea9fe14c2e01cb0a7f9.v1.cac.node-is.green, port: 40386, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mx.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-澳大利亚, type: trojan, server: 23be410a64da943de6c98c4674fb5c07.v1.cac.node-is.green, port: 47290, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: au.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-土耳其, type: trojan, server: 7eeb829ce9f946da41df84d237cdfb19.v1.cac.node-is.green, port: 40447, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: tr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-尼日利亚, type: trojan, server: 7cc49edd6efd559cf34fc815c2095f7d.v1.cac.node-is.green, port: 48905, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ng.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-越南, type: trojan, server: 4cf9ff60ab1a264c62ad1d00e31f9928.v1.cac.node-is.green, port: 42820, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: vn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴西, type: trojan, server: c1c56397ff1c2875c8c1c2afcb9c0f19.v1.cac.node-is.green, port: 43851, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: br.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-南非, type: trojan, server: e32b5f150b8e42fcb6626721571ec741.v1.cac.node-is.green, port: 45157, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: za.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-阿拉伯联合酋长国, type: trojan, server: 58078998dc4e885b414757ce56aaa8f9.v1.cac.node-is.green, port: 47269, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ae.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-缅甸, type: trojan, server: cc3d92a38ccf9c7246b7372028b22a53.v1.cac.node-is.green, port: 48822, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mm.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-希腊, type: trojan, server: f6825f6584a2cc6e2c0b45c38389587a.v1.cac.node-is.green, port: 46712, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-老挝, type: trojan, server: 74640fdad9f2c4afea259433b596dfc5.v1.cac.node-is.green, port: 46800, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: la.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-乌克兰, type: trojan, server: 27b5310a2454c492b6ab39fb8a768d4e.v1.cac.node-is.green, port: 40447, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ua.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴基斯坦, type: trojan, server: 886b14a07cca52d89eebb37b29a8d67f.v1.cac.node-is.green, port: 48603, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pk.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-斯里兰卡, type: trojan, server: 5425e76bdb394db01056bb2543661cee.v1.cac.node-is.green, port: 46914, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: lk.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-新西兰, type: trojan, server: 3d840f2db7c524041a01350ecb495873.v1.cac.node-is.green, port: 47553, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: nz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-尼泊尔, type: trojan, server: 21b80436519b4f72f18bfae56db5f6b3.v1.cac.node-is.green, port: 42874, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: np.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-塞内加尔, type: trojan, server: f2277d8b6220b5ae95e070b30e57da87.v1.cac.node-is.green, port: 44365, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: sn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.759Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-莫桑比克, type: trojan, server: f313e9a4875a08aa25d550ccecaa18e0.v1.cac.node-is.green, port: 46776, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-肯尼亚, type: trojan, server: cd063d3e686d70793181f587bdea04f2.v1.cac.node-is.green, port: 48864, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ke.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-约旦, type: trojan, server: fd6dd6f481b371a133d80a7a0feb8189.v1.cac.node-is.green, port: 49727, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: jo.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-蒙古, type: trojan, server: a1234dfaad13150681dbbf6f07684498.v1.cac.node-is.green, port: 48039, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-以色列, type: trojan, server: f8ce5d9af2fb0c3846786270ac66ac60.v1.cac.node-is.green, port: 48177, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: il.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-哈萨克斯坦, type: trojan, server: e6871e39f46f27c42cb4b9270cc775bb.v1.cac.node-is.green, port: 43243, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: kz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-印度尼西亚, type: trojan, server: 42b2b72fcb8bf6ed75a87ca9a12b7e0e.v1.cac.node-is.green, port: 44353, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: id.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-格陵兰岛, type: trojan, server: 41eff12b8c1ec4faabfaada2765ef136.v1.cac.node-is.green, port: 40229, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gl.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-意大利, type: trojan, server: 2eababf5b1ea85dbb9c2ad03e6df2267.v1.cac.node-is.green, port: 45380, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: it.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-挪威, type: trojan, server: 84db5e6c1b0c3d5b910e71130c5cc2ff.v1.cac.node-is.green, port: 45987, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: no.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-瑞典, type: trojan, server: 49dcc195df1c8785766f95218fe57171.v1.cac.node-is.green, port: 44804, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: se.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-埃及, type: trojan, server: f4216c266e320204adc3a6596b33f337.v1.cac.node-is.green, port: 40462, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: eg.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-智利, type: trojan, server: 699d9b9c2cc0cba99f1d866f6cffaf56.v1.cac.node-is.green, port: 42224, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: cl.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-柬埔寨, type: trojan, server: 66ba80715afc213f82737794d83d3688.v1.cac.node-is.green, port: 43165, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: kh.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-百慕大, type: trojan, server: 2e9afd7f43f1c514c14843e79b1f4541.v1.cac.node-is.green, port: 45998, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bm.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-孟加拉国, type: trojan, server: 873bcc7db7d3eabf21de0973e03bb5fd.v1.cac.node-is.green, port: 41645, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bd.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-葡萄牙, type: trojan, server: ee1013d55d5699b552fde006de6eb24e.v1.cac.node-is.green, port: 47019, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-西班牙, type: trojan, server: 717bdb42fbd8b6da48af9a5ec264041f.v1.cac.node-is.green, port: 44257, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: es.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-秘鲁, type: trojan, server: da7b2cf62c8d7afb1f8ae0695498e659.v1.cac.node-is.green, port: 42951, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pe.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴拉圭, type: trojan, server: 6e3314d1ad22d9449a5c9700d55a8381.v1.cac.node-is.green, port: 41152, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: py.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴拿马, type: trojan, server: af8a8dfd0aa173f14ba80b2afdbf86ab.v1.cac.node-is.green, port: 43169, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pa.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-波多黎各, type: trojan, server: e7187677ad52907d7ff4159b48dce080.v1.cac.node-is.green, port: 46924, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴布亚新几内亚, type: trojan, server: f560c93735475f7ffb6199eab8687a9f.v1.cac.node-is.green, port: 43519, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pg.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.760Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-摩洛哥, type: trojan, server: 6724d2e2dda71508c5b86a2e10598977.v1.cac.node-is.green, port: 41727, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ma.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-摩纳哥, type: trojan, server: b3e1618ab1c52cc26a17bbc4da5a7360.v1.cac.node-is.green, port: 48713, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mc.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-黑山, type: trojan, server: ca875a600369d6036315be63333d8dc0.v1.cac.node-is.green, port: 47781, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: me.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-马耳他, type: trojan, server: 53170ae2dd021d0a62936a16cce324e5.v1.cac.node-is.green, port: 40319, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-黎巴嫩, type: trojan, server: c2d886896df64175931726a944b4a232.v1.cac.node-is.green, port: 45878, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: lb.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-拉脱维亚, type: trojan, server: 3c1cfd4d5c61375ad92b2a8ab6ca805e.v1.cac.node-is.green, port: 42927, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: lv.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-立陶宛, type: trojan, server: 794073a520a46d55b850ed758770e3a0.v1.cac.node-is.green, port: 40765, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: lt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-科威特, type: trojan, server: 4a8d2d17d3b0df74c099005c944be462.v1.cac.node-is.green, port: 47718, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: kw.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.761Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-塞尔维亚, type: trojan, server: e0f4a764411f3f841d0b7ec3bb399771.v1.cac.node-is.green, port: 48464, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: rs.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.762Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-泽西岛, type: trojan, server: 6059d267276167aeec60a50759530f23.v1.cac.node-is.green, port: 47278, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: je.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.762Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-牙买加, type: trojan, server: b694b5d67b79ec861ac8742d6d9071d2.v1.cac.node-is.green, port: 42549, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: jm.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-爱尔兰, type: trojan, server: e33ea7f1365b38f2f212ac3ad5b9ae9d.v1.cac.node-is.green, port: 41094, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ie.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-冰岛, type: trojan, server: bba613ef02216f443aa0f6fc91cfeefd.v1.cac.node-is.green, port: 45291, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: is.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-匈牙利, type: trojan, server: 5bc72220c23b36fc68cb7c333629d0ed.v1.cac.node-is.green, port: 48871, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: hu.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-关岛, type: trojan, server: 7ac5df5425295cb19cf1653ad9e35098.v1.cac.node-is.green, port: 40444, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gu.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-加纳, type: trojan, server: c0d328299ba00c29504d55f6b06ebb71.v1.cac.node-is.green, port: 47835, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gh.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-格鲁吉亚, type: trojan, server: b8cc122560b013cceb2fe21de0b6f247.v1.cac.node-is.green, port: 46369, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ge.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-芬兰, type: trojan, server: 643d2f1ab7fdfd00e9efba51e22834ce.v1.cac.node-is.green, port: 44399, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: fi.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-爱沙尼亚, type: trojan, server: 7f56ba7398ad153e00fc3ebc9326ef97.v1.cac.node-is.green, port: 44139, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ee.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-多米尼加共和国, type: trojan, server: f20faf91a7815ee30abf1557be4af70e.v1.cac.node-is.green, port: 48460, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: do.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-丹麦, type: trojan, server: 79b27907cc0a0ec2325c1108801cb2a5.v1.cac.node-is.green, port: 45414, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: dk.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.763Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-捷克, type: trojan, server: a5a8798ed913f56311f743567c8f234d.v1.cac.node-is.green, port: 42441, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: cz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-塞浦路斯, type: trojan, server: 4d12ac3c186053249b6201d9479c5243.v1.cac.node-is.green, port: 43615, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: cy.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-哥伦比亚, type: trojan, server: 854846befb9fce2222845305f89e64ba.v1.cac.node-is.green, port: 48776, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: co.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-开曼群岛, type: trojan, server: 9ae3dee7d1715fe9a6a9ccf276694b30.v1.cac.node-is.green, port: 43865, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ky.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-保加利亚, type: trojan, server: 9240e105323af3bc2974e736d0761647.v1.cac.node-is.green, port: 41767, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bg.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-文莱, type: trojan, server: 7049a27abd12ea642b531e6fb808aa89.v1.cac.node-is.green, port: 49819, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-波斯尼亚和黑塞哥维纳, type: trojan, server: ebc36908962d6344482f01e83a31a8a9.v1.cac.node-is.green, port: 41494, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ba.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-玻利维亚, type: trojan, server: 6e8ab371ba0562c38313d096e98e71c2.v1.cac.node-is.green, port: 43919, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bo.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-不丹, type: trojan, server: da06cde0aaa99251345e473da244403a.v1.cac.node-is.green, port: 42467, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴林, type: trojan, server: 5c534490dfa7200b36dd9b9fecf44d80.v1.cac.node-is.green, port: 42231, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bh.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-巴哈马, type: trojan, server: 9a990148908f1c1f6162df22351710d8.v1.cac.node-is.green, port: 43279, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bs.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-阿塞拜疆, type: trojan, server: 251675f5c928e140a2ba8bc33efd0e72.v1.cac.node-is.green, port: 45651, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: az.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-奥地利, type: trojan, server: e0e4196ce65146418763572d75223a89.v1.cac.node-is.green, port: 45654, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: at.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-亚美尼亚, type: trojan, server: 9b30dc99d45af9f4284f53619c5ee67d.v1.cac.node-is.green, port: 41250, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: am.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-安哥拉, type: trojan, server: abaf414e09174c68309f2650d1636a5b.v1.cac.node-is.green, port: 49086, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ao.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-安道尔, type: trojan, server: 035e3108be4f851f123fd82c3604744e.v1.cac.node-is.green, port: 48095, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ad.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-阿尔及利亚, type: trojan, server: c74c886ea80a480b23d71c18e0744d81.v1.cac.node-is.green, port: 40298, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: dz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.764Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-阿尔巴尼亚, type: trojan, server: 23196b3cb30d7b98ca053338d93682ed.v1.cac.node-is.green, port: 40050, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: al.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-特立尼达和多巴哥, type: trojan, server: c3762a947614edc9ae88e0edc18b384e.v1.cac.node-is.green, port: 42332, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: tt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-突尼斯, type: trojan, server: c2e241e778978bf11fa9f8f8b639cf4a.v1.cac.node-is.green, port: 45996, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: tn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-瑞士, type: trojan, server: a436feb91dac5b5fc3204f1d2c7c846d.v1.cac.node-is.green, port: 49788, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ch.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-德国, type: trojan, server: b2e90327ce849ead2fcadcad740eef1c.v1.cac.node-is.green, port: 44809, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: de.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-波兰, type: trojan, server: 41a6fa7cd6642ce7166614a88c2dcc0a.v1.cac.node-is.green, port: 40098, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: pl.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-法国, type: trojan, server: d24e1fac135482945e445071c3b7cc19.v1.cac.node-is.green, port: 43733, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: fr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-荷兰, type: trojan, server: b267327b6b4e7e21c1407612aadedcb5.v1.cac.node-is.green, port: 46786, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: nl.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-斯洛文尼亚, type: trojan, server: d3ee14651977293e0dbeedac3651688c.v1.cac.node-is.green, port: 45880, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: si.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-斯洛伐克, type: trojan, server: f94c897071c22d4b4f3622858275a6a5.v1.cac.node-is.green, port: 42539, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: sk.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-罗马尼亚, type: trojan, server: 319ca7b788e669b24c42d0724115e017.v1.cac.node-is.green, port: 45018, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ro.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-北马其顿, type: trojan, server: 884ac928ddcf08ac42819bd621d81165.v1.cac.node-is.green, port: 47345, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: mk.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-摩尔多瓦, type: trojan, server: f01058edb88cad13ff51f2c38c756e5a.v1.cac.node-is.green, port: 45711, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: md.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-卢森堡, type: trojan, server: f431e9c5298fb8e4721f77f66f644098.v1.cac.node-is.green, port: 40317, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: lu.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-列支敦士登, type: trojan, server: 824e20edaa9a048460ead3ced6b57b57.v1.cac.node-is.green, port: 41654, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: li.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-哥斯达黎加, type: trojan, server: 8545181b711f4ded04537baa33be9d10.v1.cac.node-is.green, port: 42670, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: cr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-厄瓜多尔, type: trojan, server: a3d9a505edf778b9c9df1d955e71dee4.v1.cac.node-is.green, port: 47999, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ec.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-克罗地亚, type: trojan, server: a6e5d7622a186bdacc046fd1f5a62457.v1.cac.node-is.green, port: 40697, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: hr.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-马恩岛, type: trojan, server: a529232368c89d8e79008ce45507317d.v1.cac.node-is.green, port: 41707, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: im.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-委内瑞拉, type: trojan, server: 7b70be8d5621a75dea57e11f38309cb4.v1.cac.node-is.green, port: 40380, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: ve.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-洪都拉斯, type: trojan, server: ea6e4e891df0ee07e500aafc0b8ce519.v1.cac.node-is.green, port: 42601, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: hn.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-危地马拉, type: trojan, server: 15b0833a7ff5059ee5691a4355c898a2.v1.cac.node-is.green, port: 48648, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: gt.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-萨尔瓦多, type: trojan, server: 387bde57cf64802ef97df4b7db66b484.v1.cac.node-is.green, port: 40358, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: sv.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.765Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-乌兹别克斯坦, type: trojan, server: 0396fadc3258b0492b02bfd7e50ef6cc.v1.cac.node-is.green, port: 41618, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: uz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-乌拉圭, type: trojan, server: 37cee234cf4bd37584dce10a43363b52.v1.cac.node-is.green, port: 42750, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: uy.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-伯利兹, type: trojan, server: 6a8fa641273904b39e7a19d47717a97b.v1.cac.node-is.green, port: 43476, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: bz.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-比利时, type: trojan, server: 8dea4fa8cfee770149935ad31c80ef29.v1.cac.node-is.green, port: 44727, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: be.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: V1-泰国, type: trojan, server: be5d3cba3ef5c912c42d5e4a893a27b6.v1.cac.node-is.green, port: 45766, password: 4746d229-685c-4745-b3ef-0afbad5bf7ad, udp: true, skip-cert-verify: true, sni: th.cacnord.bilibili.com, network: tcp }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: proxy-groups:
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🚀 节点选择', type: select, proxies: ['🎁 自动选择', '🎁 故障转移', 剩余流量：0, '距离下次重置剩余：2 天', 套餐到期：2025-06-19, V0-美国-软件需开启跳过证书验证！！！, V0-美国-官网：www.cac.mom, V0-美国-出现问题请先更新订阅！！！, V1-香港3-HKT, V1-香港4-HKT, V1-台湾-IPv6, V1-新加坡-IPv6, V1-加拿大-IPv6, V1-日本1-IPv6, V1-美国-IPv6, V1-韩国, V1-英国, V1-马来西亚, V1-菲律宾, V1-加拿大, V1-阿根廷, V1-印度, V1-墨西哥, V1-澳大利亚, V1-土耳其, V1-尼日利亚, V1-越南, V1-巴西, V1-南非, V1-阿拉伯联合酋长国, V1-缅甸, V1-希腊, V1-老挝, V1-乌克兰, V1-巴基斯坦, V1-斯里兰卡, V1-新西兰, V1-尼泊尔, V1-塞内加尔, V1-莫桑比克, V1-肯尼亚, V1-约旦, V1-蒙古, V1-以色列, V1-哈萨克斯坦, V1-印度尼西亚, V1-格陵兰岛, V1-意大利, V1-挪威, V1-瑞典, V1-埃及, V1-智利, V1-柬埔寨, V1-百慕大, V1-孟加拉国, V1-葡萄牙, V1-西班牙, V1-秘鲁, V1-巴拉圭, V1-巴拿马, V1-波多黎各, V1-巴布亚新几内亚, V1-摩洛哥, V1-摩纳哥, V1-黑山, V1-马耳他, V1-黎巴嫩, V1-拉脱维亚, V1-立陶宛, V1-科威特, V1-塞尔维亚, V1-泽西岛, V1-牙买加, V1-爱尔兰, V1-冰岛, V1-匈牙利, V1-关岛, V1-加纳, V1-格鲁吉亚, V1-芬兰, V1-爱沙尼亚, V1-多米尼加共和国, V1-丹麦, V1-捷克, V1-塞浦路斯, V1-哥伦比亚, V1-开曼群岛, V1-保加利亚, V1-文莱, V1-波斯尼亚和黑塞哥维纳, V1-玻利维亚, V1-不丹, V1-巴林, V1-巴哈马, V1-阿塞拜疆, V1-奥地利, V1-亚美尼亚, V1-安哥拉, V1-安道尔, V1-阿尔及利亚, V1-阿尔巴尼亚, V1-特立尼达和多巴哥, V1-突尼斯, V1-瑞士, V1-德国, V1-波兰, V1-法国, V1-荷兰, V1-斯洛文尼亚, V1-斯洛伐克, V1-罗马尼亚, V1-北马其顿, V1-摩尔多瓦, V1-卢森堡, V1-列支敦士登, V1-哥斯达黎加, V1-厄瓜多尔, V1-克罗地亚, V1-马恩岛, V1-委内瑞拉, V1-洪都拉斯, V1-危地马拉, V1-萨尔瓦多, V1-乌兹别克斯坦, V1-乌拉圭, V1-伯利兹, V1-比利时, V1-泰国] }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🎁 自动选择', type: url-test, proxies: [剩余流量：0, '距离下次重置剩余：2 天', 套餐到期：2025-06-19, V0-美国-软件需开启跳过证书验证！！！, V0-美国-官网：www.cac.mom, V0-美国-出现问题请先更新订阅！！！, V1-香港3-HKT, V1-香港4-HKT, V1-台湾-IPv6, V1-新加坡-IPv6, V1-加拿大-IPv6, V1-日本1-IPv6, V1-美国-IPv6, V1-韩国, V1-英国, V1-马来西亚, V1-菲律宾, V1-加拿大, V1-阿根廷, V1-印度, V1-墨西哥, V1-澳大利亚, V1-土耳其, V1-尼日利亚, V1-越南, V1-巴西, V1-南非, V1-阿拉伯联合酋长国, V1-缅甸, V1-希腊, V1-老挝, V1-乌克兰, V1-巴基斯坦, V1-斯里兰卡, V1-新西兰, V1-尼泊尔, V1-塞内加尔, V1-莫桑比克, V1-肯尼亚, V1-约旦, V1-蒙古, V1-以色列, V1-哈萨克斯坦, V1-印度尼西亚, V1-格陵兰岛, V1-意大利, V1-挪威, V1-瑞典, V1-埃及, V1-智利, V1-柬埔寨, V1-百慕大, V1-孟加拉国, V1-葡萄牙, V1-西班牙, V1-秘鲁, V1-巴拉圭, V1-巴拿马, V1-波多黎各, V1-巴布亚新几内亚, V1-摩洛哥, V1-摩纳哥, V1-黑山, V1-马耳他, V1-黎巴嫩, V1-拉脱维亚, V1-立陶宛, V1-科威特, V1-塞尔维亚, V1-泽西岛, V1-牙买加, V1-爱尔兰, V1-冰岛, V1-匈牙利, V1-关岛, V1-加纳, V1-格鲁吉亚, V1-芬兰, V1-爱沙尼亚, V1-多米尼加共和国, V1-丹麦, V1-捷克, V1-塞浦路斯, V1-哥伦比亚, V1-开曼群岛, V1-保加利亚, V1-文莱, V1-波斯尼亚和黑塞哥维纳, V1-玻利维亚, V1-不丹, V1-巴林, V1-巴哈马, V1-阿塞拜疆, V1-奥地利, V1-亚美尼亚, V1-安哥拉, V1-安道尔, V1-阿尔及利亚, V1-阿尔巴尼亚, V1-特立尼达和多巴哥, V1-突尼斯, V1-瑞士, V1-德国, V1-波兰, V1-法国, V1-荷兰, V1-斯洛文尼亚, V1-斯洛伐克, V1-罗马尼亚, V1-北马其顿, V1-摩尔多瓦, V1-卢森堡, V1-列支敦士登, V1-哥斯达黎加, V1-厄瓜多尔, V1-克罗地亚, V1-马恩岛, V1-委内瑞拉, V1-洪都拉斯, V1-危地马拉, V1-萨尔瓦多, V1-乌兹别克斯坦, V1-乌拉圭, V1-伯利兹, V1-比利时, V1-泰国], url: 'https://captive.apple.com', interval: 86400, lazy: true }
2025-06-17T00:46:30.766Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🎁 故障转移', type: fallback, proxies: [剩余流量：0, '距离下次重置剩余：2 天', 套餐到期：2025-06-19, V0-美国-软件需开启跳过证书验证！！！, V0-美国-官网：www.cac.mom, V0-美国-出现问题请先更新订阅！！！, V1-香港3-HKT, V1-香港4-HKT, V1-台湾-IPv6, V1-新加坡-IPv6, V1-加拿大-IPv6, V1-日本1-IPv6, V1-美国-IPv6, V1-韩国, V1-英国, V1-马来西亚, V1-菲律宾, V1-加拿大, V1-阿根廷, V1-印度, V1-墨西哥, V1-澳大利亚, V1-土耳其, V1-尼日利亚, V1-越南, V1-巴西, V1-南非, V1-阿拉伯联合酋长国, V1-缅甸, V1-希腊, V1-老挝, V1-乌克兰, V1-巴基斯坦, V1-斯里兰卡, V1-新西兰, V1-尼泊尔, V1-塞内加尔, V1-莫桑比克, V1-肯尼亚, V1-约旦, V1-蒙古, V1-以色列, V1-哈萨克斯坦, V1-印度尼西亚, V1-格陵兰岛, V1-意大利, V1-挪威, V1-瑞典, V1-埃及, V1-智利, V1-柬埔寨, V1-百慕大, V1-孟加拉国, V1-葡萄牙, V1-西班牙, V1-秘鲁, V1-巴拉圭, V1-巴拿马, V1-波多黎各, V1-巴布亚新几内亚, V1-摩洛哥, V1-摩纳哥, V1-黑山, V1-马耳他, V1-黎巴嫩, V1-拉脱维亚, V1-立陶宛, V1-科威特, V1-塞尔维亚, V1-泽西岛, V1-牙买加, V1-爱尔兰, V1-冰岛, V1-匈牙利, V1-关岛, V1-加纳, V1-格鲁吉亚, V1-芬兰, V1-爱沙尼亚, V1-多米尼加共和国, V1-丹麦, V1-捷克, V1-塞浦路斯, V1-哥伦比亚, V1-开曼群岛, V1-保加利亚, V1-文莱, V1-波斯尼亚和黑塞哥维纳, V1-玻利维亚, V1-不丹, V1-巴林, V1-巴哈马, V1-阿塞拜疆, V1-奥地利, V1-亚美尼亚, V1-安哥拉, V1-安道尔, V1-阿尔及利亚, V1-阿尔巴尼亚, V1-特立尼达和多巴哥, V1-突尼斯, V1-瑞士, V1-德国, V1-波兰, V1-法国, V1-荷兰, V1-斯洛文尼亚, V1-斯洛伐克, V1-罗马尼亚, V1-北马其顿, V1-摩尔多瓦, V1-卢森堡, V1-列支敦士登, V1-哥斯达黎加, V1-厄瓜多尔, V1-克罗地亚, V1-马恩岛, V1-委内瑞拉, V1-洪都拉斯, V1-危地马拉, V1-萨尔瓦多, V1-乌兹别克斯坦, V1-乌拉圭, V1-伯利兹, V1-比利时, V1-泰国], url: 'https://captive.apple.com', interval: 7200 }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🌍 国外媒体', type: select, proxies: ['🚀 节点选择'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: 'Ⓜ️ 微软服务', type: select, proxies: ['🎯 全球直连', '🚀 节点选择'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🍎 苹果服务', type: select, proxies: ['🎯 全球直连', '🚀 节点选择'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🍎 Apple TV', type: select, proxies: ['🎯 全球直连', '🚀 节点选择'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '📢 谷歌FCM', type: select, proxies: ['🚀 节点选择', '🎯 全球直连'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🎯 全球直连', type: select, proxies: [DIRECT, '🚀 节点选择'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🛑 全球拦截', type: select, proxies: [REJECT, DIRECT] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🍃 应用净化', type: select, proxies: [REJECT, DIRECT] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - { name: '🐟 漏网之鱼', type: select, proxies: ['🚀 节点选择', '🎯 全球直连'] }
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: rules:
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - 'DOMAIN,www.cacapex.com,DIRECT'
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ADD-DIRECT,🎯 全球直连'
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,LocalAreaNetwork,🎯 全球直连'
2025-06-17T00:46:30.767Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,UnBan,🎯 全球直连'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,BanAD,🛑 全球拦截'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,BanProgramAD,🍃 应用净化'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,GoogleFCM,📢 谷歌FCM'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,GoogleCN,🎯 全球直连'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,Microsoft,Ⓜ️ 微软服务'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,Apple,🍎 苹果服务'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,AppleTV,🍎 Apple TV'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ProxyMedia,🌍 国外媒体'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ProxyLite,🚀 节点选择'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ADD-Proxy,🚀 节点选择'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ChinaDomain,🎯 全球直连'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'RULE-SET,ChinaCompanyIp,🎯 全球直连'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'GEOIP,CN,🎯 全球直连'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: - 'MATCH,🐟 漏网之鱼'
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: rule-providers:
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: ADD-DIRECT: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL2hvbmdDY2hhby9HbG9iYWxNZWRpYS1DbGFzaFJ1bGUvcmVmcy9oZWFkcy9tYWluL0FERC1ESVJFQ1QubGlzdA', path: ./providers/rule-provider_ADD-DIRECT.yaml, interval: 86400 }
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: LocalAreaNetwork: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvTG9jYWxBcmVhTmV0d29yay5saXN0', path: ./providers/rule-provider_LocalAreaNetwork.yaml, interval: 86400 }
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: UnBan: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvVW5CYW4ubGlzdA', path: ./providers/rule-provider_UnBan.yaml, interval: 86400 }
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: BanAD: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvQmFuQUQubGlzdA', path: ./providers/rule-provider_BanAD.yaml, interval: 86400 }
2025-06-17T00:46:30.768Z [WARN] [SubscriptionParser] 未知的协议类型: BanProgramAD: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvQmFuUHJvZ3JhbUFELmxpc3Q', path: ./providers/rule-provider_BanProgramAD.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: GoogleFCM: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvUnVsZXNldC9Hb29nbGVGQ00ubGlzdA', path: ./providers/rule-provider_GoogleFCM.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: GoogleCN: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvR29vZ2xlQ04ubGlzdA', path: ./providers/rule-provider_GoogleCN.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: Microsoft: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvTWljcm9zb2Z0Lmxpc3Q', path: ./providers/rule-provider_Microsoft.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: Apple: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvQXBwbGUubGlzdA', path: ./providers/rule-provider_Apple.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: AppleTV: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9yZWZzL2hlYWRzL21hc3Rlci9DbGFzaC9SdWxlc2V0L0FwcGxlVFYubGlzdA', path: ./providers/rule-provider_AppleTV.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: ProxyMedia: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvUHJveHlNZWRpYS5saXN0', path: ./providers/rule-provider_ProxyMedia.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: ProxyLite: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvUHJveHlMaXRlLmxpc3Q', path: ./providers/rule-provider_ProxyLite.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: ADD-Proxy: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL2hvbmdDY2hhby9HbG9iYWxNZWRpYS1DbGFzaFJ1bGUvcmVmcy9oZWFkcy9tYWluL0FERC1Qcm94eS5saXN0', path: ./providers/rule-provider_ADD-Proxy.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: ChinaDomain: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvQ2hpbmFEb21haW4ubGlzdA', path: ./providers/rule-provider_ChinaDomain.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [WARN] [SubscriptionParser] 未知的协议类型: ChinaCompanyIp: { type: http, behavior: classical, url: 'https://cmhk.fd1.node-is.green/getruleset?type=6&url=aHR0cHM6Ly9yYXcuZ2l0aHVidXNlcmNvbnRlbnQuY29tL0FDTDRTU1IvQUNMNFNTUi9tYXN0ZXIvQ2xhc2gvQ2hpbmFDb21wYW55SXAubGlzdA', path: ./providers/rule-provider_ChinaCompanyIp.yaml, interval: 86400 }
2025-06-17T00:46:30.769Z [INFO] [SubscriptionParser] 成功解析 0 个节点
2025-06-17T00:46:30.769Z [INFO] [SubscriptionParser] 去重前: 0 个节点，去重后: 0 个节点
2025-06-17T00:46:30.769Z [INFO] [SubscriptionParser] 批量解析完成: 0 个节点，0 个错误
2025-06-17T00:46:30.770Z [INFO] [ApiServer] POST /api/subscriptions/parse - 200 - 911ms
2025-06-17T00:46:30.776Z [INFO] [ApiServer] GET /api/nodes - 200 - 0ms
2025-06-17T00:46:39.902Z [DEBUG] [DatabaseManager] 保存订阅: 1
2025-06-17T00:46:39.903Z [INFO] [ApiServer] POST /api/subscriptions - 200 - 27ms
2025-06-17T00:46:39.910Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 1ms
2025-06-17T00:46:42.688Z [INFO] [ApiServer] GET /api/test-results - 200 - 1ms
2025-06-17T00:46:43.750Z [INFO] [ApiServer] GET /api/test-results - 200 - 1ms
2025-06-17T00:46:44.736Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 1ms
2025-06-17T00:46:58.671Z [INFO] [ApiServer] GET /api/test-results - 200 - 1ms
2025-06-17T00:47:00.776Z [INFO] [ApiServer] GET /api/test-results - 200 - 1ms
2025-06-17T00:47:02.117Z [INFO] [ApiServer] GET /api/test-results - 200 - 1ms
2025-06-17T00:47:03.449Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 1ms
