import { assertEquals, assertExists } from "@std/assert/mod.ts";
import { MihomoConfigGenerator } from "../src/config/mihomo.ts";
import { MihomoConfig, ProxyNode } from "../src/types/index.ts";

Deno.test("Mihomo配置生成器测试", () => {
  const generator = new MihomoConfigGenerator();

  // 创建测试节点
  const testNodes: ProxyNode[] = [
    {
      id: "test1",
      name: "测试VMess节点",
      type: "vmess",
      server: "vmess.example.com",
      port: 443,
      uuid: "12345678-1234-1234-1234-123456789abc",
      cipher: "auto",
      network: "tcp",
      tls: true,
    },
    {
      id: "test2",
      name: "测试VLESS节点",
      type: "vless",
      server: "vless.example.com",
      port: 443,
      uuid: "*************-4321-4321-cba987654321",
      network: "ws",
      path: "/path",
      tls: true,
    },
    {
      id: "test3",
      name: "测试Trojan节点",
      type: "trojan",
      server: "trojan.example.com",
      port: 443,
      password: "password123",
      network: "tcp",
      tls: true,
    },
    {
      id: "test4",
      name: "测试SS节点",
      type: "ss",
      server: "ss.example.com",
      port: 8388,
      cipher: "aes-256-gcm",
      password: "password456",
    },
  ];

  // 创建基础模板配置
  const template: MihomoConfig = {
    port: 7890,
    "socks-port": 7891,
    "allow-lan": true,
    mode: "rule",
    "log-level": "info",
    proxies: [],
    "proxy-groups": [
      {
        name: "PROXY",
        type: "select",
        proxies: ["DIRECT"],
      },
    ],
    rules: ["MATCH,PROXY"],
  };

  // 生成配置
  const config = generator.generateConfig(template, testNodes);

  // 验证生成的配置
  assertExists(config.proxies);
  assertEquals(config.proxies!.length, 4);

  // 验证VMess节点转换
  const vmessProxy = config.proxies!.find((p) => p.name === "测试VMess节点");
  assertExists(vmessProxy);
  assertEquals(vmessProxy!.type, "vmess");
  assertEquals(vmessProxy!.server, "vmess.example.com");
  assertEquals(vmessProxy!.port, 443);
  assertEquals(vmessProxy!.uuid, "12345678-1234-1234-1234-123456789abc");
  assertEquals(vmessProxy!.tls, true);

  // 验证VLESS节点转换
  const vlessProxy = config.proxies!.find((p) => p.name === "测试VLESS节点");
  assertExists(vlessProxy);
  assertEquals(vlessProxy!.type, "vless");
  assertEquals(vlessProxy!.network, "ws");
  assertExists(vlessProxy!["ws-opts"]);
  assertEquals((vlessProxy!["ws-opts"] as Record<string, unknown>).path, "/path");

  // 验证Trojan节点转换
  const trojanProxy = config.proxies!.find((p) => p.name === "测试Trojan节点");
  assertExists(trojanProxy);
  assertEquals(trojanProxy!.type, "trojan");
  assertEquals(trojanProxy!.password, "password123");

  // 验证SS节点转换
  const ssProxy = config.proxies!.find((p) => p.name === "测试SS节点");
  assertExists(ssProxy);
  assertEquals(ssProxy!.type, "ss");
  assertEquals(ssProxy!.cipher, "aes-256-gcm");
  assertEquals(ssProxy!.password, "password456");

  // 验证代理组更新
  assertExists(config["proxy-groups"]);
  const mainGroup = config["proxy-groups"]!.find((g) => g.name === "🚀 节点选择");
  assertExists(mainGroup);
  assertEquals(mainGroup!.proxies!.length, 4);
});

Deno.test("配置验证测试", () => {
  const generator = new MihomoConfigGenerator();

  // 测试有效配置
  const validConfig: MihomoConfig = {
    proxies: [
      {
        id: "test-id",
        name: "test",
        type: "vmess",
        server: "example.com",
        port: 443,
      },
    ],
    "proxy-groups": [
      {
        name: "PROXY",
        type: "select",
        proxies: ["test", "DIRECT"],
      },
    ],
    rules: ["MATCH,PROXY"],
  };

  const validResult = generator.validateConfig(validConfig);
  assertEquals(validResult.valid, true);
  assertEquals(validResult.errors.length, 0);

  // 测试无效配置
  const invalidConfig: MihomoConfig = {
    proxies: [],
    "proxy-groups": [
      {
        name: "PROXY",
        type: "select",
        proxies: ["nonexistent"],
      },
    ],
    rules: [],
  };

  const invalidResult = generator.validateConfig(invalidConfig);
  assertEquals(invalidResult.valid, false);
  assertEquals(invalidResult.errors.length > 0, true);
});

Deno.test("节点转换测试", () => {
  const generator = new MihomoConfigGenerator();

  // 测试不支持的节点类型
  const unsupportedNode = {
    id: "test",
    name: "不支持的节点",
    type: "unsupported",
    server: "example.com",
    port: 443,
  } as unknown as ProxyNode;

  const result = generator.convertNodeToMihomoProxy(unsupportedNode);
  assertEquals(result, null);
});
