import { ProxyNode, TestConfig, TestResult } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * 连通性测试器
 */
export class ConnectivityTester {
  private config: TestConfig;

  constructor(config: TestConfig) {
    this.config = config;
  }

  /**
   * 测试单个节点连通性
   */
  async testNode(node: ProxyNode): Promise<TestResult> {
    const startTime = Date.now();

    try {
      logger.debug(`开始测试节点连通性: ${node.name}`, "ConnectivityTester");

      // 创建测试结果对象
      const result: TestResult = {
        nodeId: node.id,
        timestamp: startTime,
        connectivity: false,
        latency: -1,
        downloadSpeed: -1,
        uploadSpeed: -1,
      };

      // 测试TCP连接
      const tcpResult = await this.testTcpConnection(node);
      result.connectivity = tcpResult.success;
      result.latency = tcpResult.latency;

      if (!result.connectivity) {
        result.error = tcpResult.error;
      }

      logger.debug(
        `节点连通性测试完成: ${node.name}, 连通性: ${result.connectivity}, 延迟: ${result.latency}ms`,
        "ConnectivityTester",
      );

      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`节点连通性测试失败: ${node.name}, ${err.message}`, "ConnectivityTester");

      return {
        nodeId: node.id,
        timestamp: startTime,
        connectivity: false,
        latency: -1,
        downloadSpeed: -1,
        uploadSpeed: -1,
        error: err.message,
      };
    }
  }

  /**
   * 测试TCP连接
   */
  private async testTcpConnection(node: ProxyNode): Promise<{
    success: boolean;
    latency: number;
    error?: string;
  }> {
    const startTime = performance.now();

    try {
      // 使用AbortController实现超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout * 1000);

      // 尝试建立TCP连接
      const conn = await Deno.connect({
        hostname: node.server,
        port: node.port,
        transport: "tcp",
      });

      clearTimeout(timeoutId);
      const endTime = performance.now();
      const latency = Math.round(endTime - startTime);

      // 关闭连接
      conn.close();

      return {
        success: true,
        latency,
      };
    } catch (error) {
      const endTime = performance.now();
      const latency = Math.round(endTime - startTime);

      const err = error instanceof Error ? error : new Error(String(error));
      // 如果是超时错误，返回超时延迟
      if (err.name === "AbortError" || latency >= this.config.timeout * 1000) {
        return {
          success: false,
          latency: this.config.timeout * 1000,
          error: "连接超时",
        };
      }

      return {
        success: false,
        latency,
        error: err.message,
      };
    }
  }

  /**
   * 批量测试节点连通性
   */
  async testNodes(nodes: ProxyNode[]): Promise<TestResult[]> {
    logger.info(`开始批量测试 ${nodes.length} 个节点的连通性`, "ConnectivityTester");

    const results: TestResult[] = [];
    const semaphore = new Semaphore(this.config.concurrency);

    const promises = nodes.map(async (node) => {
      await semaphore.acquire();
      try {
        const result = await this.testNode(node);
        results.push(result);
        return result;
      } finally {
        semaphore.release();
      }
    });

    await Promise.all(promises);

    const successCount = results.filter((r) => r.connectivity).length;
    logger.info(
      `批量连通性测试完成: ${successCount}/${nodes.length} 个节点可连通`,
      "ConnectivityTester",
    );

    return results;
  }
}

/**
 * 信号量实现，用于控制并发数
 */
class Semaphore {
  private permits: number;
  private waiting: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waiting.push(resolve);
    });
  }

  release(): void {
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!;
      resolve();
    } else {
      this.permits++;
    }
  }
}
