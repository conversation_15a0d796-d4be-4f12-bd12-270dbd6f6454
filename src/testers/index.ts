import { ProxyNode, TestConfig, TestResult } from "../types/index.ts";
import { ConnectivityTester } from "./connectivity.ts";
import { SpeedTester } from "./speed.ts";
import { logger } from "../utils/logger.ts";

/**
 * 综合测试器
 */
export class NodeTester {
  private connectivityTester: ConnectivityTester;
  private speedTester: SpeedTester;
  private config: TestConfig;

  constructor(config: TestConfig) {
    this.config = config;
    this.connectivityTester = new ConnectivityTester(config);
    this.speedTester = new SpeedTester(config);
  }

  /**
   * 完整测试单个节点
   */
  async testNode(node: ProxyNode, includeSpeed = true): Promise<TestResult> {
    logger.info(`开始完整测试节点: ${node.name}`, "NodeTester");

    try {
      // 首先测试连通性
      const connectivityResult = await this.connectivityTester.testNode(node);

      // 如果连通性测试失败，直接返回结果
      if (!connectivityResult.connectivity) {
        logger.warn(`节点连通性测试失败，跳过速度测试: ${node.name}`, "NodeTester");
        return connectivityResult;
      }

      // 如果需要测试速度且连通性正常，进行速度测试
      if (includeSpeed) {
        try {
          const speedResult = await this.speedTester.testNodeSpeed(node);

          // 合并结果
          const finalResult: TestResult = {
            ...connectivityResult,
            downloadSpeed: speedResult.downloadSpeed || -1,
            uploadSpeed: speedResult.uploadSpeed || -1,
          };

          logger.info(
            `节点完整测试完成: ${node.name}, 连通性: ${finalResult.connectivity}, ` +
              `延迟: ${finalResult.latency}ms, 下载: ${finalResult.downloadSpeed}KB/s, ` +
              `上传: ${finalResult.uploadSpeed}KB/s`,
            "NodeTester",
          );

          return finalResult;
        } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error));
          logger.warn(`节点速度测试失败: ${node.name}, ${err.message}`, "NodeTester");
          return connectivityResult; // 返回连通性测试结果
        }
      }

      return connectivityResult;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`节点完整测试失败: ${node.name}, ${err.message}`, "NodeTester");

      return {
        nodeId: node.id,
        timestamp: Date.now(),
        connectivity: false,
        latency: -1,
        downloadSpeed: -1,
        uploadSpeed: -1,
        error: err.message,
      };
    }
  }

  /**
   * 批量测试节点
   */
  async testNodes(
    nodes: ProxyNode[],
    options: {
      includeSpeed?: boolean;
      onProgress?: (completed: number, total: number, current?: TestResult) => void;
    } = {},
  ): Promise<TestResult[]> {
    const { includeSpeed = true, onProgress } = options;

    logger.info(
      `开始批量测试 ${nodes.length} 个节点 (包含速度测试: ${includeSpeed})`,
      "NodeTester",
    );

    const results: TestResult[] = [];
    const semaphore = new Semaphore(this.config.concurrency);
    let completed = 0;

    const promises = nodes.map(async (node, index) => {
      await semaphore.acquire();
      try {
        const result = await this.testNode(node, includeSpeed);
        results[index] = result;
        completed++;

        // 调用进度回调
        if (onProgress) {
          onProgress(completed, nodes.length, result);
        }

        return result;
      } finally {
        semaphore.release();
      }
    });

    await Promise.all(promises);

    // 统计结果
    const stats = this.calculateStats(results);
    logger.info(
      `批量测试完成: ${stats.total} 个节点, ` +
        `可连通: ${stats.connected}, 平均延迟: ${stats.avgLatency}ms, ` +
        `平均下载速度: ${stats.avgDownloadSpeed}KB/s`,
      "NodeTester",
    );

    return results;
  }

  /**
   * 快速连通性测试（不包含速度测试）
   */
  quickTest(nodes: ProxyNode[]): Promise<TestResult[]> {
    logger.info(`开始快速连通性测试 ${nodes.length} 个节点`, "NodeTester");
    return this.connectivityTester.testNodes(nodes);
  }

  /**
   * 计算测试统计信息
   */
  private calculateStats(results: TestResult[]) {
    const connected = results.filter((r) => r.connectivity);
    const validLatencies = connected.filter((r) => r.latency > 0).map((r) => r.latency);
    const validDownloadSpeeds = connected.filter((r) => r.downloadSpeed > 0).map((r) =>
      r.downloadSpeed
    );

    return {
      total: results.length,
      connected: connected.length,
      avgLatency: validLatencies.length > 0
        ? Math.round(validLatencies.reduce((a, b) => a + b, 0) / validLatencies.length)
        : 0,
      avgDownloadSpeed: validDownloadSpeeds.length > 0
        ? Math.round(validDownloadSpeeds.reduce((a, b) => a + b, 0) / validDownloadSpeeds.length)
        : 0,
    };
  }

  /**
   * 更新测试配置
   */
  updateConfig(config: Partial<TestConfig>) {
    this.config = { ...this.config, ...config };
    this.connectivityTester = new ConnectivityTester(this.config);
    this.speedTester = new SpeedTester(this.config);
    logger.info("测试配置已更新", "NodeTester", config);
  }
}

/**
 * 信号量实现
 */
class Semaphore {
  private permits: number;
  private waiting: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waiting.push(resolve);
    });
  }

  release(): void {
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!;
      resolve();
    } else {
      this.permits++;
    }
  }
}

// 导出默认测试配置
export const DEFAULT_TEST_CONFIG: TestConfig = {
  timeout: 10, // 10秒超时
  concurrency: 10, // 10个并发
  testUrl: "http://www.gstatic.com/generate_204", // Google连通性测试
  speedTestSize: 1024, // 1MB速度测试
};
