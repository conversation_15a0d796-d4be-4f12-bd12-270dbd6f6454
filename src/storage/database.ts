import { DB } from "@db/sqlite";
import { FilterOptions, ProxyNode, Subscription, TestResult } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * 数据库管理器
 */
export class DatabaseManager {
  private db!: DB;
  private dbPath: string;

  constructor(dbPath = "data/nodes.db") {
    this.dbPath = dbPath;
    this.initDatabase();
  }

  /**
   * 初始化数据库
   */
  private initDatabase() {
    try {
      // 确保数据目录存在
      Deno.mkdirSync("data", { recursive: true });

      this.db = new DB(this.dbPath);
      this.createTables();
      logger.info(`数据库初始化完成: ${this.dbPath}`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`数据库初始化失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 创建数据表
   */
  private createTables() {
    // 订阅表
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        url TEXT NOT NULL UNIQUE,
        last_update INTEGER NOT NULL,
        node_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT 1,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )
    `);

    // 节点表
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS nodes (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        server TEXT NOT NULL,
        port INTEGER NOT NULL,
        config TEXT NOT NULL,
        subscription_id TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (subscription_id) REFERENCES subscriptions (id)
      )
    `);

    // 测试结果表
    this.db.execute(`
      CREATE TABLE IF NOT EXISTS test_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        node_id TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        connectivity BOOLEAN NOT NULL,
        latency INTEGER NOT NULL,
        download_speed INTEGER NOT NULL,
        upload_speed INTEGER NOT NULL,
        error TEXT,
        FOREIGN KEY (node_id) REFERENCES nodes (id)
      )
    `);

    // 创建索引
    this.db.execute(`CREATE INDEX IF NOT EXISTS idx_nodes_type ON nodes (type)`);
    this.db.execute(`CREATE INDEX IF NOT EXISTS idx_nodes_server ON nodes (server)`);
    this.db.execute(
      `CREATE INDEX IF NOT EXISTS idx_test_results_node_id ON test_results (node_id)`,
    );
    this.db.execute(
      `CREATE INDEX IF NOT EXISTS idx_test_results_timestamp ON test_results (timestamp)`,
    );
  }

  /**
   * 保存订阅
   */
  saveSubscription(subscription: Subscription): void {
    try {
      this.db.query(
        `INSERT OR REPLACE INTO subscriptions 
         (id, name, url, last_update, node_count, is_active) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          subscription.id,
          subscription.name,
          subscription.url,
          subscription.lastUpdate,
          subscription.nodeCount,
          subscription.isActive,
        ],
      );
      logger.debug(`保存订阅: ${subscription.name}`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`保存订阅失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 获取所有订阅
   */
  getSubscriptions(): Subscription[] {
    try {
      const rows = this.db.query(`
        SELECT id, name, url, last_update, node_count, is_active 
        FROM subscriptions 
        ORDER BY created_at DESC
      `);

      return rows.map((row) => ({
        id: row[0] as string,
        name: row[1] as string,
        url: row[2] as string,
        lastUpdate: row[3] as number,
        nodeCount: row[4] as number,
        isActive: Boolean(row[5]),
      }));
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`获取订阅列表失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 保存节点
   */
  saveNode(node: ProxyNode, subscriptionId?: string): void {
    try {
      this.db.query(
        `INSERT OR REPLACE INTO nodes 
         (id, name, type, server, port, config, subscription_id) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          node.id,
          node.name,
          node.type,
          node.server,
          node.port,
          JSON.stringify(node),
          subscriptionId || null,
        ],
      );
      logger.debug(`保存节点: ${node.name}`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`保存节点失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 批量保存节点
   */
  saveNodes(nodes: ProxyNode[], subscriptionId?: string): void {
    try {
      this.db.transaction(() => {
        for (const node of nodes) {
          this.saveNode(node, subscriptionId);
        }
      });
      logger.info(`批量保存 ${nodes.length} 个节点`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`批量保存节点失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 获取节点列表
   */
  getNodes(filters?: FilterOptions): ProxyNode[] {
    try {
      let sql = `
        SELECT id, name, type, server, port, config 
        FROM nodes 
        WHERE 1=1
      `;
      const params: (string | number)[] = [];

      // 应用过滤条件
      if (filters?.protocols && filters.protocols.length > 0) {
        sql += ` AND type IN (${filters.protocols.map(() => "?").join(",")})`;
        params.push(...filters.protocols);
      }

      if (filters?.keywords && filters.keywords.length > 0) {
        const keywordConditions = filters.keywords.map(() => "name LIKE ?").join(" OR ");
        sql += ` AND (${keywordConditions})`;
        params.push(...filters.keywords.map((k) => `%${k}%`));
      }

      sql += ` ORDER BY created_at DESC`;

      const rows = this.db.query(sql, params);

      return rows.map((row) => {
        const config = JSON.parse(row[5] as string) as ProxyNode;
        return config;
      });
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`获取节点列表失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 保存测试结果
   */
  saveTestResult(result: TestResult): void {
    try {
      this.db.query(
        `INSERT INTO test_results 
         (node_id, timestamp, connectivity, latency, download_speed, upload_speed, error) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          result.nodeId,
          result.timestamp,
          result.connectivity,
          result.latency,
          result.downloadSpeed,
          result.uploadSpeed,
          result.error || null,
        ],
      );
      logger.debug(`保存测试结果: ${result.nodeId}`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`保存测试结果失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 批量保存测试结果
   */
  saveTestResults(results: TestResult[]): void {
    try {
      this.db.transaction(() => {
        for (const result of results) {
          this.saveTestResult(result);
        }
      });
      logger.info(`批量保存 ${results.length} 个测试结果`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`批量保存测试结果失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 获取节点的最新测试结果
   */
  getLatestTestResults(nodeIds?: string[]): Map<string, TestResult> {
    try {
      let sql = `
        SELECT DISTINCT node_id, timestamp, connectivity, latency, download_speed, upload_speed, error
        FROM test_results t1
        WHERE timestamp = (
          SELECT MAX(timestamp) 
          FROM test_results t2 
          WHERE t2.node_id = t1.node_id
        )
      `;
      const params: (string | number)[] = [];

      if (nodeIds && nodeIds.length > 0) {
        sql += ` AND node_id IN (${nodeIds.map(() => "?").join(",")})`;
        params.push(...nodeIds);
      }

      const rows = this.db.query(sql, params);
      const results = new Map<string, TestResult>();

      for (const row of rows) {
        const result: TestResult = {
          nodeId: row[0] as string,
          timestamp: row[1] as number,
          connectivity: Boolean(row[2]),
          latency: row[3] as number,
          downloadSpeed: row[4] as number,
          uploadSpeed: row[5] as number,
          error: row[6] as string || undefined,
        };
        results.set(result.nodeId, result);
      }

      return results;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`获取最新测试结果失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 清理旧的测试结果
   */
  cleanupOldResults(daysToKeep = 30): void {
    try {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      const result = this.db.query(
        `DELETE FROM test_results WHERE timestamp < ?`,
        [cutoffTime],
      );
      logger.info(`清理了 ${result} 条旧测试结果`, "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`清理旧测试结果失败: ${err.message}`, "DatabaseManager");
      throw err;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    try {
      this.db.close();
      logger.info("数据库连接已关闭", "DatabaseManager");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error(`关闭数据库连接失败: ${err.message}`, "DatabaseManager");
    }
  }
}
