import { BaseParser } from "./base.ts";
import { ProxyNode } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * Trojan协议解析器
 */
export class TrojanParser extends BaseParser {
  readonly protocol = "trojan";

  parse(url: string): ProxyNode | null {
    try {
      if (!this.validateUrl(url)) {
        logger.warn(`无效的Trojan URL格式: ${url}`, "TrojanParser");
        return null;
      }

      const parsed = new URL(url);
      const params = this.parseQueryParams(parsed.search);

      // 验证必需字段
      if (!parsed.hostname || !parsed.port || !parsed.username) {
        logger.warn("Trojan URL缺少必需字段", "TrojanParser");
        return null;
      }

      const node: ProxyNode = {
        id: this.generateNodeId(parsed.hostname, parseInt(parsed.port), "trojan"),
        name: this.cleanNodeName(
          decodeURIComponent(parsed.hash?.slice(1) || `Trojan-${parsed.hostname}`),
        ),
        type: "trojan",
        server: parsed.hostname,
        port: this.parsePort(parsed.port, 443),
        password: parsed.username,
        network: params.type || "tcp",
        tls: true, // Trojan默认使用TLS
      };

      // TLS相关字段
      if (params.sni) node.sni = params.sni;
      if (params.alpn) node.alpn = this.parseArray(params.alpn);
      if (params.fp) node.fingerprint = params.fp;

      // 跳过证书验证
      if (params.allowInsecure) {
        node.skipCertVerify = this.parseBoolean(params.allowInsecure);
      }

      // 传输协议特定字段
      switch (node.network) {
        case "ws":
        case "websocket":
          if (params.path) node.path = params.path;
          if (params.host) {
            node.headers = { Host: params.host };
          }
          break;

        case "h2":
        case "http":
          if (params.path) node.path = params.path;
          if (params.host) node.host = params.host;
          break;

        case "grpc":
          if (params.serviceName) node.serviceName = params.serviceName;
          if (params.mode) node.grpcMode = params.mode;
          break;

        case "kcp":
          if (params.headerType) node.kcpHeaderType = params.headerType;
          if (params.seed) node.kcpSeed = params.seed;
          break;

        case "quic":
          if (params.quicSecurity) node.quicSecurity = params.quicSecurity;
          if (params.key) node.quicKey = params.key;
          if (params.headerType) node.quicHeaderType = params.headerType;
          break;
      }

      logger.debug(`成功解析Trojan节点: ${node.name}`, "TrojanParser");
      return node;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析Trojan URL失败: ${errorMessage}`, "TrojanParser", { url });
      return null;
    }
  }

  /**
   * 生成Trojan URL
   */
  generateUrl(node: ProxyNode): string {
    try {
      const params = new URLSearchParams();

      // 基础参数
      if (node.network && node.network !== "tcp") {
        params.set("type", node.network);
      }

      // TLS参数
      if (node.sni) params.set("sni", node.sni);
      if (node.alpn) params.set("alpn", node.alpn.join(","));
      if (node.fingerprint) params.set("fp", node.fingerprint as string);
      if (node.skipCertVerify) params.set("allowInsecure", "1");

      // 传输协议参数
      if (node.path) params.set("path", node.path);
      if (node.host) params.set("host", node.host);
      if (node.serviceName) params.set("serviceName", node.serviceName as string);

      const url = new URL(`trojan://${node.password}@${node.server}:${node.port}`);
      if (params.toString()) {
        url.search = params.toString();
      }
      url.hash = encodeURIComponent(node.name);

      return url.toString();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`生成Trojan URL失败: ${errorMessage}`, "TrojanParser");
      throw error;
    }
  }
}
