import { BaseParser } from "./base.ts";
import { ProxyNode } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * VLESS协议解析器
 */
export class VlessParser extends BaseParser {
  readonly protocol = "vless";

  parse(url: string): ProxyNode | null {
    try {
      if (!this.validateUrl(url)) {
        logger.warn(`无效的VLESS URL格式: ${url}`, "VlessParser");
        return null;
      }

      const parsed = new URL(url);
      const params = this.parseQueryParams(parsed.search);

      // 验证必需字段
      if (!parsed.hostname || !parsed.port || !parsed.username) {
        logger.warn("VLESS URL缺少必需字段", "VlessParser");
        return null;
      }

      const node: ProxyNode = {
        id: this.generateNodeId(parsed.hostname, parseInt(parsed.port), "vless"),
        name: this.cleanNodeName(
          decodeURIComponent(parsed.hash?.slice(1) || `VLESS-${parsed.hostname}`),
        ),
        type: "vless",
        server: parsed.hostname,
        port: this.parsePort(parsed.port, 443),
        uuid: parsed.username,
        network: params.type || "tcp",
        tls: params.security === "tls" || params.security === "reality",
      };

      // TLS相关字段
      if (node.tls) {
        if (params.sni) node.sni = params.sni;
        if (params.alpn) node.alpn = this.parseArray(params.alpn);
        if (params.fp) node.fingerprint = params.fp;
      }

      // Reality相关字段
      if (params.security === "reality") {
        if (params.pbk) node.publicKey = params.pbk;
        if (params.sid) node.shortId = params.sid;
        if (params.spx) node.spiderX = params.spx;
      }

      // 传输协议特定字段
      switch (node.network) {
        case "ws":
        case "websocket":
          if (params.path) node.path = params.path;
          if (params.host) {
            node.headers = { Host: params.host };
          }
          break;

        case "h2":
        case "http":
          if (params.path) node.path = params.path;
          if (params.host) node.host = params.host;
          break;

        case "grpc":
          if (params.serviceName) node.serviceName = params.serviceName;
          if (params.mode) node.grpcMode = params.mode;
          break;

        case "kcp":
          if (params.headerType) node.kcpHeaderType = params.headerType;
          if (params.seed) node.kcpSeed = params.seed;
          break;

        case "quic":
          if (params.quicSecurity) node.quicSecurity = params.quicSecurity;
          if (params.key) node.quicKey = params.key;
          if (params.headerType) node.quicHeaderType = params.headerType;
          break;
      }

      // 流控制
      if (params.flow) {
        node.flow = params.flow;
      }

      logger.debug(`成功解析VLESS节点: ${node.name}`, "VlessParser");
      return node;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析VLESS URL失败: ${errorMessage}`, "VlessParser", { url });
      return null;
    }
  }

  /**
   * 生成VLESS URL
   */
  generateUrl(node: ProxyNode): string {
    try {
      const params = new URLSearchParams();

      // 基础参数
      params.set("type", node.network || "tcp");
      if (node.tls) {
        params.set("security", "tls");
        if (node.sni) params.set("sni", node.sni);
        if (node.alpn) params.set("alpn", node.alpn.join(","));
        if (node.fingerprint) params.set("fp", node.fingerprint as string);
      }

      // 传输协议参数
      if (node.path) params.set("path", node.path);
      if (node.host) params.set("host", node.host);
      if (node.serviceName) params.set("serviceName", node.serviceName as string);
      if (node.flow) params.set("flow", node.flow as string);

      const url = new URL(`vless://${node.uuid}@${node.server}:${node.port}`);
      url.search = params.toString();
      url.hash = encodeURIComponent(node.name);

      return url.toString();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`生成VLESS URL失败: ${errorMessage}`, "VlessParser");
      throw error;
    }
  }
}
