import { parse as parseYaml } from "https://deno.land/std@0.224.0/yaml/parse.ts";
import { stringify as stringifyYaml } from "https://deno.land/std@0.224.0/yaml/stringify.ts";
import { MihomoConfig, ProxyNode, ProxyGroup } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * Mihomo配置生成器
 */
export class MihomoConfigGenerator {
  /**
   * 读取YAML模板文件
   */
  async readTemplate(templatePath: string): Promise<MihomoConfig> {
    try {
      const content = await Deno.readTextFile(templatePath);
      const config = parseYaml(content) as MihomoConfig;

      logger.info(`成功读取模板文件: ${templatePath}`, "MihomoConfigGenerator");
      return config;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`读取模板文件失败: ${errorMessage}`, "MihomoConfigGenerator", { templatePath });
      throw new Error(`读取模板文件失败: ${errorMessage}`);
    }
  }

  /**
   * 将ProxyNode转换为Mihomo代理配置
   */
  convertNodeToMihomoProxy(node: ProxyNode): Record<string, unknown> | null {
    const baseProxy = {
      name: node.name,
      type: node.type,
      server: node.server,
      port: node.port,
    };

    switch (node.type) {
      case "vmess":
        return {
          ...baseProxy,
          uuid: node.uuid,
          alterId: 0,
          cipher: node.cipher || "auto",
          network: node.network || "tcp",
          tls: node.tls || false,
          ...(node.sni && { servername: node.sni }),
          ...(node.alpn && { alpn: node.alpn }),
          ...(node.network === "ws" && {
            "ws-opts": {
              path: node.path || "/",
              headers: node.headers || {},
            },
          }),
          ...(node.network === "h2" && {
            "h2-opts": {
              host: node.host ? [node.host] : [],
              path: node.path || "/",
            },
          }),
          ...(node.network === "grpc" && {
            "grpc-opts": {
              "grpc-service-name": node.serviceName || "",
            },
          }),
        };

      case "vless":
        return {
          ...baseProxy,
          uuid: node.uuid,
          network: node.network || "tcp",
          tls: node.tls || false,
          ...(node.sni && { servername: node.sni }),
          ...(node.alpn && { alpn: node.alpn }),
          ...(node.flow ? { flow: node.flow } : {}),
          ...(node.network === "ws" && {
            "ws-opts": {
              path: node.path || "/",
              headers: node.headers || {},
            },
          }),
          ...(node.network === "h2" && {
            "h2-opts": {
              host: node.host ? [node.host] : [],
              path: node.path || "/",
            },
          }),
          ...(node.network === "grpc" && {
            "grpc-opts": {
              "grpc-service-name": node.serviceName || "",
            },
          }),
          // Reality支持
          ...(node.publicKey ? {
            "reality-opts": {
              "public-key": node.publicKey,
              "short-id": node.shortId || "",
            },
          } : {}),
        };

      case "trojan":
        return {
          ...baseProxy,
          password: node.password,
          network: node.network || "tcp",
          tls: true, // Trojan默认使用TLS
          ...(node.sni && { servername: node.sni }),
          ...(node.alpn && { alpn: node.alpn }),
          ...(node.skipCertVerify ? { "skip-cert-verify": true } : {}),
          ...(node.network === "ws" && {
            "ws-opts": {
              path: node.path || "/",
              headers: node.headers || {},
            },
          }),
          ...(node.network === "grpc" && {
            "grpc-opts": {
              "grpc-service-name": node.serviceName || "",
            },
          }),
        };

      case "ss":
        return {
          ...baseProxy,
          cipher: node.cipher,
          password: node.password,
          ...(node.plugin ? { plugin: node.plugin } : {}),
          ...(node.pluginOpts ? { "plugin-opts": node.pluginOpts } : {}),
          ...(node.udp !== undefined && { udp: node.udp }),
        };

      default:
        logger.warn(`不支持的代理类型: ${node.type}`, "MihomoConfigGenerator");
        return null;
    }
  }

  /**
   * 生成完整的Mihomo配置
   */
  generateConfig(
    template: MihomoConfig,
    nodes: ProxyNode[],
    options: {
      groupName?: string;
      insertToGroups?: boolean;
      backupOriginal?: boolean;
    } = {},
  ): MihomoConfig {
    try {
      const { groupName = "🚀 节点选择", insertToGroups = true } = options;

      // 深拷贝模板配置
      const config: MihomoConfig = JSON.parse(JSON.stringify(template));

      // 转换节点为Mihomo代理配置
      const mihomoProxies = nodes
        .map((node) => this.convertNodeToMihomoProxy(node))
        .filter((proxy) => proxy !== null) as Record<string, unknown>[];

      // 设置代理列表
      config.proxies = [...(config.proxies || []), ...mihomoProxies as ProxyNode[]];

      // 如果需要插入到代理组
      if (insertToGroups && config["proxy-groups"]) {
        const proxyNames = mihomoProxies.map((p) => p.name as string);

        // 查找或创建主选择组
        let mainGroup = config["proxy-groups"].find((g) => g.name === groupName);
        if (!mainGroup) {
          mainGroup = {
            name: groupName,
            type: "select",
            proxies: [],
          };
          config["proxy-groups"].unshift(mainGroup);
        }

        // 添加新节点到主选择组
        if (!mainGroup.proxies) mainGroup.proxies = [];
        mainGroup.proxies.push(...proxyNames);

        // 去重
        mainGroup.proxies = [...new Set(mainGroup.proxies)];

        // 更新其他使用PROXY的组
        for (const group of config["proxy-groups"]) {
          if (group.proxies && group.proxies.includes("PROXY")) {
            const index = group.proxies.indexOf("PROXY");
            group.proxies[index] = groupName;
          }
        }
      }

      logger.info(
        `成功生成Mihomo配置: ${mihomoProxies.length} 个代理节点`,
        "MihomoConfigGenerator",
      );

      return config;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`生成Mihomo配置失败: ${errorMessage}`, "MihomoConfigGenerator");
      throw error;
    }
  }

  /**
   * 保存配置到文件
   */
  async saveConfig(config: MihomoConfig, outputPath: string): Promise<void> {
    try {
      // 确保输出目录存在
      const dir = outputPath.substring(0, outputPath.lastIndexOf("/"));
      if (dir) {
        await Deno.mkdir(dir, { recursive: true });
      }

      // 转换为YAML并保存
      const yamlContent = stringifyYaml(config, {
        indent: 2,
        lineWidth: 120,
      });

      await Deno.writeTextFile(outputPath, yamlContent);

      logger.info(`配置文件已保存: ${outputPath}`, "MihomoConfigGenerator");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`保存配置文件失败: ${errorMessage}`, "MihomoConfigGenerator", { outputPath });
      throw error;
    }
  }

  /**
   * 备份原始配置文件
   */
  async backupConfig(originalPath: string): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const backupPath = `${originalPath}.backup.${timestamp}`;

      await Deno.copyFile(originalPath, backupPath);

      logger.info(`配置文件已备份: ${backupPath}`, "MihomoConfigGenerator");
      return backupPath;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`备份配置文件失败: ${errorMessage}`, "MihomoConfigGenerator");
      throw error;
    }
  }

  /**
   * 验证配置文件格式
   */
  validateConfig(config: MihomoConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // 检查必需字段
      if (!config.proxies || !Array.isArray(config.proxies)) {
        errors.push("缺少proxies字段或格式错误");
      }

      if (!config["proxy-groups"] || !Array.isArray(config["proxy-groups"])) {
        errors.push("缺少proxy-groups字段或格式错误");
      }

      if (!config.rules || !Array.isArray(config.rules)) {
        errors.push("缺少rules字段或格式错误");
      }

      // 检查代理组引用
      if (config["proxy-groups"]) {
        const proxyNames = new Set((config.proxies || []).map((p) => p.name));
        const groupNames = new Set(config["proxy-groups"].map((g) => g.name));

        for (const group of config["proxy-groups"]) {
          if (group.proxies) {
            for (const proxyName of group.proxies) {
              if (
                !proxyNames.has(proxyName) && !groupNames.has(proxyName) &&
                !["DIRECT", "REJECT", "PASS"].includes(proxyName)
              ) {
                errors.push(`代理组 ${group.name} 引用了不存在的代理: ${proxyName}`);
              }
            }
          }
        }
      }

      return { valid: errors.length === 0, errors };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`配置验证失败: ${errorMessage}`);
      return { valid: false, errors };
    }
  }
}
