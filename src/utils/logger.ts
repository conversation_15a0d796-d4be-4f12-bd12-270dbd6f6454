import { LogEntry } from "../types/index.ts";

export class Logger {
  private static instance: Logger;
  private logFile: string;

  private constructor() {
    this.logFile = `logs/app-${new Date().toISOString().split("T")[0]}.log`;
    this.ensureLogDir();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private async ensureLogDir() {
    try {
      await Deno.mkdir("logs", { recursive: true });
    } catch (error) {
      if (!(error instanceof Deno.errors.AlreadyExists)) {
        console.error("创建日志目录失败:", error);
      }
    }
  }

  private async writeToFile(entry: LogEntry) {
    try {
      const logLine = `${new Date(entry.timestamp).toISOString()} [${entry.level.toUpperCase()}] ${
        entry.module ? `[${entry.module}] ` : ""
      }${entry.message}${entry.data ? ` ${JSON.stringify(entry.data)}` : ""}\n`;

      await Deno.writeTextFile(this.logFile, logLine, { append: true });
    } catch (error) {
      console.error("写入日志文件失败:", error);
    }
  }

  private log(level: LogEntry["level"], message: string, module?: string, data?: Record<string, unknown>) {
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      message,
      module,
      data,
    };

    // 控制台输出
    const consoleMessage = `[${level.toUpperCase()}] ${module ? `[${module}] ` : ""}${message}`;
    switch (level) {
      case "error":
        console.error(consoleMessage, data || "");
        break;
      case "warn":
        console.warn(consoleMessage, data || "");
        break;
      case "debug":
        console.debug(consoleMessage, data || "");
        break;
      default:
        console.log(consoleMessage, data || "");
    }

    // 写入文件
    this.writeToFile(entry);
  }

  info(message: string, module?: string, data?: Record<string, unknown>) {
    this.log("info", message, module, data);
  }

  warn(message: string, module?: string, data?: Record<string, unknown>) {
    this.log("warn", message, module, data);
  }

  error(message: string, module?: string, data?: Record<string, unknown>) {
    this.log("error", message, module, data);
  }

  debug(message: string, module?: string, data?: Record<string, unknown>) {
    this.log("debug", message, module, data);
  }
}

export const logger = Logger.getInstance();
